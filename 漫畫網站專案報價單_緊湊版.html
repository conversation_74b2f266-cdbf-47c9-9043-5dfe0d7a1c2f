<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>海水不可斗量工作室 | 漫畫網站專案報價單</title>
    <style>
        @page {
            size: A4;
            margin: 12mm;
        }
        
        @media print {
            body { 
                margin: 0; 
                font-size: 10px;
                line-height: 1.2;
            }
            .no-print { display: none; }
            .page-break { page-break-before: always; }
        }
        
        body {
            font-family: 'Microsoft JhengHei', 'PingFang TC', 'Noto Sans TC', sans-serif;
            line-height: 1.3;
            color: #000;
            margin: 0;
            padding: 12px;
            font-size: 11px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 15px;
            border-bottom: 2px solid #000;
            padding-bottom: 10px;
        }
        
        .logo {
            max-height: 40px;
            margin-bottom: 8px;
        }
        
        h1 {
            margin: 5px 0;
            font-size: 16px;
            font-weight: bold;
        }
        
        h2 {
            border-left: 3px solid #000;
            padding-left: 6px;
            margin: 12px 0 6px 0;
            font-size: 12px;
            font-weight: bold;
        }
        
        h3 {
            margin: 8px 0 4px 0;
            font-size: 11px;
            font-weight: bold;
        }
        
        .basic-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 12px;
            font-size: 10px;
        }
        
        .basic-info div {
            border: 1px solid #000;
            padding: 6px;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 8px 0;
            font-size: 9px;
        }
        
        th, td {
            border: 1px solid #000;
            padding: 4px;
            text-align: left;
            vertical-align: top;
        }
        
        th {
            background-color: #f0f0f0;
            font-weight: bold;
            font-size: 9px;
        }
        
        .module-table th:first-child {
            width: 15%;
        }
        
        .module-table th:nth-child(2) {
            width: 45%;
        }
        
        .module-table th:last-child {
            width: 40%;
        }
        
        .cost-table th:first-child {
            width: 40%;
        }
        
        .cost-table th:nth-child(2) {
            width: 25%;
        }
        
        .cost-table th:last-child {
            width: 35%;
        }
        
        .total-section {
            border: 2px solid #000;
            padding: 8px;
            margin: 8px 0;
            text-align: center;
            background-color: #f8f8f8;
            font-weight: bold;
        }
        
        .notes-section {
            font-size: 9px;
            line-height: 1.2;
        }
        
        .notes-section ol, .notes-section ul {
            margin: 4px 0;
            padding-left: 12px;
        }
        
        .notes-section li {
            margin-bottom: 2px;
        }
        
        .payment-info {
            border: 1px solid #000;
            padding: 6px;
            margin: 8px 0;
            background-color: #f8f8f8;
            font-size: 10px;
        }
        
        .contact-section {
            border: 1px solid #000;
            padding: 6px;
            margin: 8px 0;
            background-color: #f0f0f0;
            font-size: 10px;
        }
        
        .footer {
            text-align: center;
            margin-top: 10px;
            padding-top: 6px;
            border-top: 1px solid #000;
            font-size: 9px;
            font-style: italic;
        }
        
        .print-button {
            position: fixed;
            top: 10px;
            right: 10px;
            background-color: #000;
            color: white;
            border: none;
            padding: 6px 12px;
            cursor: pointer;
            font-size: 11px;
        }
        
        .print-button:hover {
            background-color: #333;
        }
        
        .compact-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin: 8px 0;
        }
        
        strong {
            font-weight: bold;
        }
        
        .highlight {
            background-color: #f0f0f0;
            padding: 1px 2px;
        }
    </style>
</head>
<body>
    <button class="print-button no-print" onclick="window.print()">列印 / 轉PDF</button>
    
    <div class="header">
        <img src="logo.png" alt="海水不可斗量工作室" class="logo">
        <h1>🧾 海水不可斗量工作室 | 漫畫網站專案報價單</h1>
    </div>
    
    <div class="basic-info">
        <div>
            <strong>開發廠商：</strong> 海水不可斗量工作室<br>
            <strong>客戶名稱：</strong> 漫畫網站專案
        </div>
        <div>
            <strong>報價日期：</strong> 2025年9月18日<br>
            <strong>報價有效期限：</strong> 30日內有效
        </div>
    </div>
    
    <h2>📦 專案內容與功能規劃</h2>
    
    <table class="module-table">
        <thead>
            <tr>
                <th>模組分類</th>
                <th>功能項目</th>
                <th>說明</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td><strong>1. 會員系統</strong></td>
                <td>註冊/登入（含社交登入）<br>多層級訂閱（月/年）<br>個人書櫃與閱讀歷史<br>收藏與評分系統</td>
                <td>提供完整會員管理與訂閱機制</td>
            </tr>
            <tr>
                <td><strong>2. 創作者後台</strong></td>
                <td>作品上傳與排序<br>章節管理與定時發布<br>收益統計（會員數與收費）</td>
                <td>支援創作者內容管理與收益追蹤</td>
            </tr>
            <tr>
                <td><strong>3. 閱讀體驗</strong></td>
                <td>響應式閱讀器（手機/平板）<br>圖片壓縮與格式優化（WebP）<br>懶加載與虛擬滾動<br>書籤紀錄功能<br>離線閱讀（PWA）</td>
                <td>提供流暢且優化的閱讀體驗，PWA另計</td>
            </tr>
            <tr>
                <td><strong>4. 付費系統</strong></td>
                <td>綠界或藍新金流整合<br>訂閱管理與自動續費<br>電子發票整合</td>
                <td>支援台灣主流金流與發票系統</td>
            </tr>
            <tr>
                <td><strong>5. 法律與效能考量</strong></td>
                <td>法律合規與使用條款<br>版權保護（浮水印）<br>圖片加載優化<br>API限流與快取策略</td>
                <td>確保網站安全、合法與高效運作</td>
            </tr>
            <tr>
                <td><strong>6. 周邊商品商城</strong></td>
                <td>購物車與結帳系統<br>初期使用 7-11 賣貨便<br>未申請公司行號，未來可升級</td>
                <td>支援商品販售與物流整合</td>
            </tr>
        </tbody>
    </table>
    
    <h2>💰 費用明細</h2>

    <table class="cost-table">
        <thead>
            <tr>
                <th>項目</th>
                <th>金額（新台幣）</th>
                <th>備註</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>網站開發費用</td>
                <td>$23,000</td>
                <td>含上述功能模組（不含PWA）</td>
            </tr>
            <tr>
                <td>網域費用</td>
                <td>$1,200/年</td>
                <td>由工作室代為申請</td>
            </tr>
            <tr>
                <td>離線閱讀（PWA）</td>
                <td>另計</td>
                <td>可依需求追加報價</td>
            </tr>
            <tr>
                <td>發票稅額</td>
                <td>5%</td>
                <td>開立正式發票</td>
            </tr>
        </tbody>
    </table>

    <h3>🔧 客戶自行申請服務</h3>
    <table class="cost-table">
        <thead>
            <tr>
                <th>服務項目</th>
                <th>說明</th>
                <th>備註</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>Heroku 伺服器</td>
                <td>網站主機服務</td>
                <td>客戶自行申請帳號，提供API金鑰給工作室</td>
            </tr>
            <tr>
                <td>Cloudinary 圖庫</td>
                <td>圖片儲存與優化服務</td>
                <td>客戶自行申請帳號，提供API金鑰給工作室</td>
            </tr>
            <tr>
                <td>Auth0 認證系統</td>
                <td>會員登入與認證服務</td>
                <td>客戶自行申請帳號，提供API金鑰給工作室</td>
            </tr>
            <tr>
                <td>金流服務</td>
                <td>綠界或藍新金流</td>
                <td>客戶自行申請商店帳號，提供API金鑰給工作室</td>
            </tr>
        </tbody>
    </table>

    <div class="total-section">
        <div><strong>工作室收費總計（未稅）：NT$24,200</strong></div>
        <div><strong>工作室收費總計（含稅）：NT$25,410</strong></div>
        <div class="highlight"><strong>訂金35% = 8,894元</strong></div>
    </div>
    
    <div class="compact-grid">
        <div>
            <h2>📌 備註</h2>
            <div class="notes-section">
                <ol>
                    <li>本報價不含後續維護與升級費用，若有需求可另行簽訂維護合約</li>
                    <li>專案開發期預估為 <strong>6–8 週</strong>，視客戶確認流程與素材提供進度調整</li>
                    <li>客戶需自行申請以下服務帳號並提供 API 金鑰：Heroku、Cloudinary、Auth0、金流服務</li>
                    <li>工作室僅收取網站開發費用與網域費用，其他第三方服務費用由客戶直接支付</li>
                    <li>支付訂金完成後即安排啟動網站開發</li>
                </ol>
            </div>
            
            <div class="payment-info">
                <h3>💳 付款資訊</h3>
                <p><strong>支付方式：</strong> 016高雄銀行 台中分行</p>
                <p><strong>帳號：</strong> 229102623611</p>
                <p><strong>戶名：</strong> 卉田國際有限公司（海水不可斗量工作室）</p>
            </div>
        </div>
        
        <div>
            <div class="contact-section">
                <h3>📞 聯繫資訊</h3>
                <p><strong>公司名稱：</strong> 海水不可斗量工作室</p>
                <p><strong>負責人：</strong> 白翼輔</p>
                <p><strong>聯繫電話：</strong> 0912-305-910</p>
                <p><strong>電子郵件：</strong> <EMAIL></p>
                <p><strong>地址：</strong> 臺中市西區中興街183號四樓</p>
            </div>
        </div>
    </div>
    
    <div class="footer">
        <p><em>此報價單有效期限為 30 天，過期後價格可能調整</em></p>
        <p><em>海水不可斗量工作室 | 專業網站開發服務</em></p>
    </div>
</body>
</html>
